{"anchor()": {"syntax": "anchor( <anchor-name>? && <anchor-side>, <length-percentage>? )", "groups": ["CSS Positioning"], "status": "experimental", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/anchor"}, "anchor-size()": {"syntax": "anchor-size( [ <anchor-name> || <anchor-size> ]? , <length-percentage>? )", "groups": ["CSS Positioning"], "status": "experimental", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/anchor-size"}, "attr()": {"syntax": "attr( <attr-name> <type-or-unit>? [, <attr-fallback> ]? )", "groups": ["CSS Generated Content"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/attr"}, "blur()": {"syntax": "blur( <length> )", "groups": ["Filter Effects"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/filter-function/blur"}, "brightness()": {"syntax": "brightness( <number-percentage> )", "groups": ["Filter Effects"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/filter-function/brightness"}, "calc()": {"syntax": "calc( <calc-sum> )", "groups": ["CSS Units", "CSS Lengths"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/calc"}, "circle()": {"syntax": "circle( [ <shape-radius> ]? [ at <position> ]? )", "groups": ["CSS Shapes"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/basic-shape/circle"}, "clamp()": {"syntax": "clamp( <calc-sum>#{3} )", "groups": ["CSS Fonts"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/clamp"}, "conic-gradient()": {"syntax": "conic-gradient( [ from <angle> ]? [ at <position> ]?, <angular-color-stop-list> )", "groups": ["CSS Backgrounds and Borders", "CSS Color"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/gradient/conic-gradient"}, "contrast()": {"syntax": "contrast( [ <number-percentage> ] )", "groups": ["Filter Effects", "CSS Color"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/filter-function/contrast"}, "counter()": {"syntax": "counter( <custom-ident>, <counter-style>? )", "groups": ["CSS Lists and Counters"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/counter"}, "counters()": {"syntax": "counters( <custom-ident>, <string>, <counter-style>? )", "groups": ["CSS Lists and Counters"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/counters"}, "cross-fade()": {"syntax": "cross-fade( <cf-mixing-image> , <cf-final-image>? )", "groups": ["Filter Effects", "CSS Color"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/cross-fade"}, "drop-shadow()": {"syntax": "drop-shadow( <length>{2,3} <color>? )", "groups": ["Filter Effects", "CSS Color"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/filter-function/drop-shadow"}, "element()": {"syntax": "element( <id-selector> )", "groups": ["CSS Miscellaneous"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/element"}, "ellipse()": {"syntax": "ellipse( [ <shape-radius>{2} ]? [ at <position> ]? )", "groups": ["CSS Shapes"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/basic-shape/ellipse"}, "env()": {"syntax": "env( <custom-ident> , <declaration-value>? )", "groups": ["CSS Box Model"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/env"}, "fit-content()": {"syntax": "fit-content( [ <length> | <percentage> ] )", "groups": ["CSS Box Model"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/fit-content"}, "grayscale()": {"syntax": "grayscale( <number-percentage> )", "groups": ["Filter Effects", "CSS Color"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/filter-function/grayscale"}, "hsl()": {"syntax": "hsl( <hue> <percentage> <percentage> [ / <alpha-value> ]? ) | hsl( <hue>, <percentage>, <percentage>, <alpha-value>? )", "groups": ["CSS Color"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/color_value/hsl"}, "hsla()": {"syntax": "hsla( <hue> <percentage> <percentage> [ / <alpha-value> ]? ) | hsla( <hue>, <percentage>, <percentage>, <alpha-value>? )", "groups": ["CSS Color"], "status": "nonstandard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/color_value/hsla"}, "hue-rotate()": {"syntax": "hue-rotate( <angle> )", "groups": ["Filter Effects", "CSS Color"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/filter-function/hue-rotate"}, "image()": {"syntax": "image( <image-tags>? [ <image-src>? , <color>? ]! )", "groups": ["CSS Images"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/image/image"}, "image-set()": {"syntax": "image-set( <image-set-option># )", "groups": ["CSS Images"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/image/image-set"}, "inset()": {"syntax": "inset( <length-percentage>{1,4} [ round <'border-radius'> ]? )", "groups": ["CSS Shapes"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/basic-shape/inset"}, "invert()": {"syntax": "invert( <number-percentage> )", "groups": ["Filter Effects", "CSS Color"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/filter-function/invert"}, "leader()": {"syntax": "leader( <leader-type> )", "groups": ["CSS Miscellaneous"], "status": "nonstandard"}, "linear-gradient()": {"syntax": "linear-gradient( [ <angle> | to <side-or-corner> ]? , <color-stop-list> )", "groups": ["CSS Backgrounds and Borders", "CSS Color"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/gradient/linear-gradient"}, "matrix()": {"syntax": "matrix( <number>#{6} )", "groups": ["CSS Transforms"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/transform-function/matrix"}, "matrix3d()": {"syntax": "matrix3d( <number>#{16} )", "groups": ["CSS Transforms"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/transform-function/matrix3d"}, "max()": {"syntax": "max( <calc-sum># )", "groups": ["CSS Units", "CSS Lengths"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/max"}, "min()": {"syntax": "min( <calc-sum># )", "groups": ["CSS Units", "CSS Lengths"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/min"}, "minmax()": {"syntax": "minmax( [ <length> | <percentage> | min-content | max-content | auto ] , [ <length> | <percentage> | <flex> | min-content | max-content | auto ] )", "groups": ["CSS Units", "CSS Lengths"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/minmax"}, "oklab()": {"syntax": "oklab( [ <percentage> | <number> | none] [ <percentage> | <number> | none] [ <percentage> | <number> | none] [ / [<alpha-value> | none] ]? )", "groups": ["CSS Color"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/color_value/oklab"}, "oklch()": {"syntax": "oklch( [ <percentage> | <number> | none] [ <percentage> | <number> | none] [ <hue> | none] [ / [<alpha-value> | none] ]? )", "groups": ["CSS Color"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/color_value/oklch"}, "opacity()": {"syntax": "opacity( [ <number-percentage> ] )", "groups": ["Filter Effects", "CSS Color"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/filter-function/opacity"}, "path()": {"syntax": "path( [ <fill-rule>, ]? <string> )", "groups": ["CSS Shapes", "CSS Motion Path"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/basic-shape/path"}, "paint()": {"syntax": "paint( <ident>, <declaration-value>? )", "groups": ["CSS Backgrounds and Borders"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/image/paint"}, "perspective()": {"syntax": "perspective( <length> )", "groups": ["CSS Transforms"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/transform-function/perspective"}, "polygon()": {"syntax": "polygon( <fill-rule>? , [ <length-percentage> <length-percentage> ]# )", "groups": ["CSS Shapes"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/basic-shape/polygon"}, "radial-gradient()": {"syntax": "radial-gradient( [ <ending-shape> || <size> ]? [ at <position> ]? , <color-stop-list> )", "groups": ["CSS Backgrounds and Borders", "CSS Color"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/gradient/radial-gradient"}, "ray()": {"syntax": "ray( <angle> && <ray-size>? && contain? && [at <position>]? )", "groups": ["CSS Motion Path"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/ray"}, "repeating-linear-gradient()": {"syntax": "repeating-linear-gradient( [ <angle> | to <side-or-corner> ]? , <color-stop-list> )", "groups": ["CSS Backgrounds and Borders", "CSS Color"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/repeating-linear-gradient"}, "repeating-radial-gradient()": {"syntax": "repeating-radial-gradient( [ <ending-shape> || <size> ]? [ at <position> ]? , <color-stop-list> )", "groups": ["CSS Backgrounds and Borders", "CSS Color"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/gradient/repeating-linear-gradient"}, "rgb()": {"syntax": "rgb( <percentage>{3} [ / <alpha-value> ]? ) | rgb( <number>{3} [ / <alpha-value> ]? ) | rgb( <percentage>#{3} , <alpha-value>? ) | rgb( <number>#{3} , <alpha-value>? )", "groups": ["CSS Color"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/color_value/rgb"}, "rgba()": {"syntax": "rgba( <percentage>{3} [ / <alpha-value> ]? ) | rgba( <number>{3} [ / <alpha-value> ]? ) | rgba( <percentage>#{3} , <alpha-value>? ) | rgba( <number>#{3} , <alpha-value>? )", "groups": ["CSS Color"], "status": "nonstandard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/color_value/rgba"}, "rotate()": {"syntax": "rotate( [ <angle> | <zero> ] )", "groups": ["CSS Transforms"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/transform-function/rotate"}, "rotate3d()": {"syntax": "rotate3d( <number> , <number> , <number> , [ <angle> | <zero> ] )", "groups": ["CSS Transforms"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/transform-function/rotate3d"}, "rotateX()": {"syntax": "rotateX( [ <angle> | <zero> ] )", "groups": ["CSS Transforms"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/transform-function/rotateX"}, "rotateY()": {"syntax": "rotateY( [ <angle> | <zero> ] )", "groups": ["CSS Transforms"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/transform-function/rotateY"}, "rotateZ()": {"syntax": "rotateZ( [ <angle> | <zero> ] )", "groups": ["CSS Transforms"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/transform-function/rotateZ"}, "saturate()": {"syntax": "saturate( <number-percentage> )", "groups": ["Filter Effects", "CSS Color"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/filter-function/saturate"}, "scale()": {"syntax": "scale( <number> , <number>? )", "groups": ["CSS Transforms"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/transform-function/scale"}, "scale3d()": {"syntax": "scale3d( <number> , <number> , <number> )", "groups": ["CSS Transforms"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/transform-function/scale3d"}, "scaleX()": {"syntax": "scaleX( <number> )", "groups": ["CSS Transforms"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/transform-function/scaleX"}, "scaleY()": {"syntax": "scaleY( <number> )", "groups": ["CSS Transforms"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/transform-function/scaleY"}, "scaleZ()": {"syntax": "scaleZ( <number> )", "groups": ["CSS Transforms"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/transform-function/scaleZ"}, "scroll()": {"syntax": "scroll([<axis> || <scroller>]?)", "groups": ["CSS Animations"], "status": "experimental", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/animation-timeline/scroll"}, "skew()": {"syntax": "skew( [ <angle> | <zero> ] , [ <angle> | <zero> ]? )", "groups": ["CSS Transforms"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/transform-function/skew"}, "skewX()": {"syntax": "skewX( [ <angle> | <zero> ] )", "groups": ["CSS Transforms"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/transform-function/skewX"}, "skewY()": {"syntax": "skewY( [ <angle> | <zero> ] )", "groups": ["CSS Transforms"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/transform-function/skewY"}, "sepia()": {"syntax": "sepia( <number-percentage> )", "groups": ["Filter Effects", "CSS Color"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/filter-function/sepia"}, "target-counter()": {"syntax": "target-counter( [ <string> | <url> ] , <custom-ident> , <counter-style>? )", "groups": ["CSS Lists and Counters"], "status": "nonstandard"}, "target-counters()": {"syntax": "target-counters( [ <string> | <url> ] , <custom-ident> , <string> , <counter-style>? )", "groups": ["CSS Lists and Counters"], "status": "nonstandard"}, "target-text()": {"syntax": "target-text( [ <string> | <url> ] , [ content | before | after | first-letter ]? )", "groups": ["CSS Miscellaneous"], "status": "nonstandard"}, "translate()": {"syntax": "translate( <length-percentage> , <length-percentage>? )", "groups": ["CSS Transforms"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/transform-function/translate"}, "translate3d()": {"syntax": "translate3d( <length-percentage> , <length-percentage> , <length> )", "groups": ["CSS Transforms"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/transform-function/translate3d"}, "translateX()": {"syntax": "translateX( <length-percentage> )", "groups": ["CSS Transforms"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/transform-function/translateX"}, "translateY()": {"syntax": "translateY( <length-percentage> )", "groups": ["CSS Transforms"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/transform-function/translateY"}, "translateZ()": {"syntax": "translateZ( <length> )", "groups": ["CSS Transforms"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/transform-function/translateZ"}, "var()": {"syntax": "var( <custom-property-name> , <declaration-value>? )", "groups": ["CSS Miscellaneous"], "status": "standard", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/var"}, "view()": {"syntax": "view([<axis> || <'view-timeline-inset'>]?)", "groups": ["CSS Animations"], "status": "experimental", "mdn_url": "https://developer.mozilla.org/docs/Web/CSS/animation-timeline/view"}}