hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@antfu/install-pkg@1.1.0':
    '@antfu/install-pkg': private
  '@antfu/utils@0.7.10':
    '@antfu/utils': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.0':
    '@babel/core': private
  '@babel/generator@7.28.0':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.27.3':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-member-expression-to-functions@7.27.1':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.27.1':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-replace-supers@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-replace-supers': private
  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helpers@7.27.6':
    '@babel/helpers': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/plugin-proposal-decorators@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-proposal-decorators': private
  '@babel/plugin-syntax-decorators@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-decorators': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-meta': private
  '@babel/plugin-syntax-jsx@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-jsx': private
  '@babel/plugin-syntax-typescript@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-typescript': private
  '@babel/plugin-transform-typescript@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-typescript': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.0':
    '@babel/traverse': private
  '@babel/types@7.28.1':
    '@babel/types': private
  '@css-render/plugin-bem@0.15.14(css-render@0.15.14)':
    '@css-render/plugin-bem': private
  '@css-render/vue3-ssr@0.15.14(vue@3.5.18)':
    '@css-render/vue3-ssr': private
  '@emotion/hash@0.8.0':
    '@emotion/hash': private
  '@esbuild/win32-x64@0.25.8':
    '@esbuild/win32-x64': private
  '@iconify/types@2.0.0':
    '@iconify/types': private
  '@iconify/utils@2.3.0':
    '@iconify/utils': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@juggle/resize-observer@3.4.0':
    '@juggle/resize-observer': private
  '@polka/url@1.0.0-next.29':
    '@polka/url': private
  '@quansync/fs@0.1.3':
    '@quansync/fs': private
  '@rolldown/pluginutils@1.0.0-beta.19':
    '@rolldown/pluginutils': private
  '@rollup/pluginutils@5.2.0(rollup@4.45.1)':
    '@rollup/pluginutils': private
  '@rollup/rollup-win32-x64-msvc@4.45.1':
    '@rollup/rollup-win32-x64-msvc': private
  '@sec-ant/readable-stream@0.4.1':
    '@sec-ant/readable-stream': private
  '@sindresorhus/merge-streams@4.0.0':
    '@sindresorhus/merge-streams': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/katex@0.16.7':
    '@types/katex': private
  '@types/lodash-es@4.17.12':
    '@types/lodash-es': private
  '@types/lodash@4.17.20':
    '@types/lodash': private
  '@unocss/astro@66.3.3(vite@7.0.5(jiti@2.5.1))(vue@3.5.18)':
    '@unocss/astro': private
  '@unocss/cli@66.3.3':
    '@unocss/cli': private
  '@unocss/config@66.3.3':
    '@unocss/config': private
  '@unocss/core@66.3.3':
    '@unocss/core': private
  '@unocss/extractor-arbitrary-variants@66.3.3':
    '@unocss/extractor-arbitrary-variants': private
  '@unocss/inspector@66.3.3(vue@3.5.18)':
    '@unocss/inspector': private
  '@unocss/postcss@66.3.3(postcss@8.5.6)':
    '@unocss/postcss': private
  '@unocss/preset-attributify@66.3.3':
    '@unocss/preset-attributify': private
  '@unocss/preset-icons@66.3.3':
    '@unocss/preset-icons': private
  '@unocss/preset-mini@66.3.3':
    '@unocss/preset-mini': private
  '@unocss/preset-tagify@66.3.3':
    '@unocss/preset-tagify': private
  '@unocss/preset-typography@66.3.3':
    '@unocss/preset-typography': private
  '@unocss/preset-uno@66.3.3':
    '@unocss/preset-uno': private
  '@unocss/preset-web-fonts@66.3.3':
    '@unocss/preset-web-fonts': private
  '@unocss/preset-wind3@66.3.3':
    '@unocss/preset-wind3': private
  '@unocss/preset-wind4@66.3.3':
    '@unocss/preset-wind4': private
  '@unocss/preset-wind@66.3.3':
    '@unocss/preset-wind': private
  '@unocss/rule-utils@66.3.3':
    '@unocss/rule-utils': private
  '@unocss/transformer-attributify-jsx@66.3.3':
    '@unocss/transformer-attributify-jsx': private
  '@unocss/transformer-compile-class@66.3.3':
    '@unocss/transformer-compile-class': private
  '@unocss/transformer-directives@66.3.3':
    '@unocss/transformer-directives': private
  '@unocss/transformer-variant-group@66.3.3':
    '@unocss/transformer-variant-group': private
  '@unocss/vite@66.3.3(vite@7.0.5(jiti@2.5.1))(vue@3.5.18)':
    '@unocss/vite': private
  '@vue/babel-helper-vue-transform-on@1.4.0':
    '@vue/babel-helper-vue-transform-on': private
  '@vue/babel-plugin-jsx@1.4.0(@babel/core@7.28.0)':
    '@vue/babel-plugin-jsx': private
  '@vue/babel-plugin-resolve-type@1.4.0(@babel/core@7.28.0)':
    '@vue/babel-plugin-resolve-type': private
  '@vue/compiler-core@3.5.18':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.18':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.18':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.18':
    '@vue/compiler-ssr': private
  '@vue/devtools-core@7.7.7(vite@7.0.5(jiti@2.5.1))(vue@3.5.18)':
    '@vue/devtools-core': private
  '@vue/devtools-core@7.7.7(vite@7.0.5)(vue@3.5.18)':
    '@vue/devtools-core': private
  '@vue/devtools-kit@7.7.7':
    '@vue/devtools-kit': private
  '@vue/devtools-shared@7.7.7':
    '@vue/devtools-shared': private
  '@vue/reactivity@3.5.18':
    '@vue/reactivity': private
  '@vue/runtime-core@3.5.18':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.5.18':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.5.18(vue@3.5.18)':
    '@vue/server-renderer': private
  '@vue/shared@3.5.18':
    '@vue/shared': private
  acorn@8.15.0:
    acorn: private
  anymatch@3.1.3:
    anymatch: private
  async-validator@4.2.5:
    async-validator: private
  binary-extensions@2.3.0:
    binary-extensions: private
  birpc@2.5.0:
    birpc: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.1:
    browserslist: private
  bundle-name@4.1.0:
    bundle-name: private
  cac@6.7.14:
    cac: private
  caniuse-lite@1.0.30001727:
    caniuse-lite: private
  chokidar@3.6.0:
    chokidar: private
  colorette@2.0.20:
    colorette: private
  confbox@0.1.8:
    confbox: private
  consola@3.4.2:
    consola: private
  convert-source-map@2.0.0:
    convert-source-map: private
  copy-anything@3.0.5:
    copy-anything: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-render@0.15.14:
    css-render: private
  css-tree@3.1.0:
    css-tree: private
  csstype@3.1.3:
    csstype: private
  date-fns-tz@3.2.0(date-fns@3.6.0):
    date-fns-tz: private
  date-fns@3.6.0:
    date-fns: private
  debug@4.4.1:
    debug: private
  default-browser-id@5.0.0:
    default-browser-id: private
  default-browser@5.2.1:
    default-browser: private
  define-lazy-prop@3.0.0:
    define-lazy-prop: private
  defu@6.1.4:
    defu: private
  destr@2.0.5:
    destr: private
  duplexer@0.1.2:
    duplexer: private
  electron-to-chromium@1.5.190:
    electron-to-chromium: private
  entities@4.5.0:
    entities: private
  error-stack-parser-es@0.1.5:
    error-stack-parser-es: private
  esbuild@0.25.8:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  estree-walker@2.0.2:
    estree-walker: private
  evtd@0.2.4:
    evtd: private
  execa@9.6.0:
    execa: private
  exsolve@1.0.7:
    exsolve: private
  fdir@6.4.6(picomatch@4.0.3):
    fdir: private
  figures@6.1.0:
    figures: private
  fill-range@7.1.1:
    fill-range: private
  fs-extra@11.3.0:
    fs-extra: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-stream@9.0.1:
    get-stream: private
  glob-parent@5.1.2:
    glob-parent: private
  globals@15.15.0:
    globals: private
  graceful-fs@4.2.11:
    graceful-fs: private
  gzip-size@6.0.0:
    gzip-size: private
  highlight.js@11.11.1:
    highlight.js: private
  hookable@5.5.3:
    hookable: private
  human-signals@8.0.1:
    human-signals: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-docker@3.0.0:
    is-docker: private
  is-extglob@2.1.1:
    is-extglob: private
  is-glob@4.0.3:
    is-glob: private
  is-inside-container@1.0.0:
    is-inside-container: private
  is-number@7.0.0:
    is-number: private
  is-plain-obj@4.1.0:
    is-plain-obj: private
  is-stream@4.0.1:
    is-stream: private
  is-unicode-supported@2.1.0:
    is-unicode-supported: private
  is-what@4.1.16:
    is-what: private
  is-wsl@3.1.0:
    is-wsl: private
  isexe@2.0.0:
    isexe: private
  jiti@2.5.1:
    jiti: private
  js-tokens@4.0.0:
    js-tokens: private
  jsesc@3.1.0:
    jsesc: private
  json5@2.2.3:
    json5: private
  jsonfile@6.1.0:
    jsonfile: private
  kolorist@1.8.0:
    kolorist: private
  local-pkg@1.1.1:
    local-pkg: private
  lodash-es@4.17.21:
    lodash-es: private
  lodash@4.17.21:
    lodash: private
  lru-cache@5.1.1:
    lru-cache: private
  magic-string@0.30.17:
    magic-string: private
  mdn-data@2.12.2:
    mdn-data: private
  mitt@3.0.1:
    mitt: private
  mlly@1.7.4:
    mlly: private
  mrmime@2.0.1:
    mrmime: private
  ms@2.1.3:
    ms: private
  nanoid@5.1.5:
    nanoid: private
  node-fetch-native@1.6.6:
    node-fetch-native: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  npm-run-path@6.0.0:
    npm-run-path: private
  ofetch@1.4.1:
    ofetch: private
  open@10.2.0:
    open: private
  package-manager-detector@1.3.0:
    package-manager-detector: private
  parse-ms@4.0.0:
    parse-ms: private
  path-key@3.1.1:
    path-key: private
  pathe@2.0.3:
    pathe: private
  perfect-debounce@1.0.0:
    perfect-debounce: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.3:
    picomatch: private
  pkg-types@2.2.0:
    pkg-types: private
  postcss@8.5.6:
    postcss: private
  pretty-ms@9.2.0:
    pretty-ms: private
  quansync@0.2.10:
    quansync: private
  readdirp@3.6.0:
    readdirp: private
  rfdc@1.4.1:
    rfdc: private
  rollup@4.45.1:
    rollup: private
  run-applescript@7.0.0:
    run-applescript: private
  seemly@0.3.10:
    seemly: private
  semver@6.3.1:
    semver: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  signal-exit@4.1.0:
    signal-exit: private
  sirv@3.0.1:
    sirv: private
  source-map-js@1.2.1:
    source-map-js: private
  speakingurl@14.0.1:
    speakingurl: private
  strip-final-newline@4.0.0:
    strip-final-newline: private
  superjson@2.2.2:
    superjson: private
  tinyexec@1.0.1:
    tinyexec: private
  tinyglobby@0.2.14:
    tinyglobby: private
  to-regex-range@5.0.1:
    to-regex-range: private
  totalist@3.0.1:
    totalist: private
  treemate@0.3.11:
    treemate: private
  ufo@1.6.1:
    ufo: private
  unconfig@7.3.2:
    unconfig: private
  unicorn-magic@0.3.0:
    unicorn-magic: private
  universalify@2.0.1:
    universalify: private
  unplugin-utils@0.2.4:
    unplugin-utils: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  vdirs@0.1.8(vue@3.5.18):
    vdirs: private
  vite-hot-client@2.1.0(vite@7.0.5(jiti@2.5.1)):
    vite-hot-client: private
  vite-hot-client@2.1.0(vite@7.0.5):
    vite-hot-client: private
  vite-plugin-inspect@0.8.9(rollup@4.45.1)(vite@7.0.5(jiti@2.5.1)):
    vite-plugin-inspect: private
  vite-plugin-inspect@0.8.9(rollup@4.45.1)(vite@7.0.5):
    vite-plugin-inspect: private
  vite-plugin-vue-inspector@5.3.2(vite@7.0.5(jiti@2.5.1)):
    vite-plugin-vue-inspector: private
  vite-plugin-vue-inspector@5.3.2(vite@7.0.5):
    vite-plugin-vue-inspector: private
  vooks@0.2.12(vue@3.5.18):
    vooks: private
  vue-flow-layout@0.1.1(vue@3.5.18):
    vue-flow-layout: private
  vueuc@0.4.64(vue@3.5.18):
    vueuc: private
  which@2.0.2:
    which: private
  wsl-utils@0.1.0:
    wsl-utils: private
  yallist@3.1.1:
    yallist: private
  yoctocolors@2.1.1:
    yoctocolors: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.4
pendingBuilds: []
prunedAt: Thu, 24 Jul 2025 02:21:16 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped:
  - '@esbuild/aix-ppc64@0.25.8'
  - '@esbuild/android-arm64@0.25.8'
  - '@esbuild/android-arm@0.25.8'
  - '@esbuild/android-x64@0.25.8'
  - '@esbuild/darwin-arm64@0.25.8'
  - '@esbuild/darwin-x64@0.25.8'
  - '@esbuild/freebsd-arm64@0.25.8'
  - '@esbuild/freebsd-x64@0.25.8'
  - '@esbuild/linux-arm64@0.25.8'
  - '@esbuild/linux-arm@0.25.8'
  - '@esbuild/linux-ia32@0.25.8'
  - '@esbuild/linux-loong64@0.25.8'
  - '@esbuild/linux-mips64el@0.25.8'
  - '@esbuild/linux-ppc64@0.25.8'
  - '@esbuild/linux-riscv64@0.25.8'
  - '@esbuild/linux-s390x@0.25.8'
  - '@esbuild/linux-x64@0.25.8'
  - '@esbuild/netbsd-arm64@0.25.8'
  - '@esbuild/netbsd-x64@0.25.8'
  - '@esbuild/openbsd-arm64@0.25.8'
  - '@esbuild/openbsd-x64@0.25.8'
  - '@esbuild/openharmony-arm64@0.25.8'
  - '@esbuild/sunos-x64@0.25.8'
  - '@esbuild/win32-arm64@0.25.8'
  - '@esbuild/win32-ia32@0.25.8'
  - '@rollup/rollup-android-arm-eabi@4.45.1'
  - '@rollup/rollup-android-arm64@4.45.1'
  - '@rollup/rollup-darwin-arm64@4.45.1'
  - '@rollup/rollup-darwin-x64@4.45.1'
  - '@rollup/rollup-freebsd-arm64@4.45.1'
  - '@rollup/rollup-freebsd-x64@4.45.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.45.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.45.1'
  - '@rollup/rollup-linux-arm64-gnu@4.45.1'
  - '@rollup/rollup-linux-arm64-musl@4.45.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.45.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.45.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.45.1'
  - '@rollup/rollup-linux-riscv64-musl@4.45.1'
  - '@rollup/rollup-linux-s390x-gnu@4.45.1'
  - '@rollup/rollup-linux-x64-gnu@4.45.1'
  - '@rollup/rollup-linux-x64-musl@4.45.1'
  - '@rollup/rollup-win32-arm64-msvc@4.45.1'
  - '@rollup/rollup-win32-ia32-msvc@4.45.1'
  - fsevents@2.3.3
storeDir: D:\.pnpm-store\v10
virtualStoreDir: D:\Code\cbtest\counterdown\node_modules\.pnpm
virtualStoreDirMaxLength: 60
