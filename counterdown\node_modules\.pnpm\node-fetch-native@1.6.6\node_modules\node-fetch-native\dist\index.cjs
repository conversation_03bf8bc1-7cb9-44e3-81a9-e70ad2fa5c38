"use strict";Object.defineProperty(exports,"__esModule",{value:!0});const node=require("./node.cjs");require("node:http"),require("node:https"),require("node:zlib"),require("node:stream"),require("node:buffer"),require("node:util"),require("./shared/node-fetch-native.DhEqb06g.cjs"),require("node:url"),require("node:net"),require("node:fs"),require("node:path");const o=!!globalThis.process?.env?.FORCE_NODE_FETCH,fetch=!o&&globalThis.fetch||node.fetch,Blob=!o&&globalThis.Blob||node.Blob,File=!o&&globalThis.File||node.File,FormData=!o&&globalThis.FormData||node.FormData,Headers=!o&&globalThis.Headers||node.Headers,Request=!o&&globalThis.Request||node.Request,Response=!o&&globalThis.Response||node.Response,AbortController=!o&&globalThis.AbortController||node.AbortController;exports.AbortError=node.AbortError,exports.FetchError=node.FetchError,exports.blobFrom=node.blobFrom,exports.blobFromSync=node.blobFromSync,exports.fileFrom=node.fileFrom,exports.fileFromSync=node.fileFromSync,exports.isRedirect=node.isRedirect,exports.AbortController=AbortController,exports.Blob=Blob,exports.File=File,exports.FormData=FormData,exports.Headers=Headers,exports.Request=Request,exports.Response=Response,exports.default=fetch,exports.fetch=fetch;
