var i=Object.defineProperty;var e=(r,t)=>i(r,"name",{value:t,configurable:!0});import{fetch as l,Blob as m,File as p,FormData as a,Headers as n,Request as s,Response as b,AbortController as f}from"./node.mjs";import"node:fs";import"node:path";import"node:http";import"node:https";import"node:zlib";import"node:stream";import"node:buffer";import"node:util";import"./shared/node-fetch-native.DfbY2q-x.mjs";import"node:url";import"node:net";var c=Object.defineProperty,u=e((r,t)=>c(r,"name",{value:t,configurable:!0}),"a");function o(r,t){if(!(r in globalThis))try{globalThis[r]=t}catch{}}e(o,"e"),u(o,"polyfill"),o("fetch",l),o("Blob",m),o("File",p),o("FormData",a),o("Headers",n),o("Request",s),o("Response",b),o("AbortController",f);
