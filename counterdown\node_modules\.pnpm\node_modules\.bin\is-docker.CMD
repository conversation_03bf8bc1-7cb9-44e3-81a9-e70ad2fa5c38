@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=D:\Code\cbtest\counterdown\node_modules\.pnpm\is-docker@3.0.0\node_modules\is-docker\node_modules;D:\Code\cbtest\counterdown\node_modules\.pnpm\is-docker@3.0.0\node_modules;D:\Code\cbtest\counterdown\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=D:\Code\cbtest\counterdown\node_modules\.pnpm\is-docker@3.0.0\node_modules\is-docker\node_modules;D:\Code\cbtest\counterdown\node_modules\.pnpm\is-docker@3.0.0\node_modules;D:\Code\cbtest\counterdown\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\is-docker\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\is-docker\cli.js" %*
)
