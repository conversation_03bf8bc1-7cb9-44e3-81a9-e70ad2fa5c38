#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/Code/cbtest/counterdown/node_modules/.pnpm/@unocss+cli@66.3.3/node_modules/@unocss/cli/bin/node_modules:/mnt/d/Code/cbtest/counterdown/node_modules/.pnpm/@unocss+cli@66.3.3/node_modules/@unocss/cli/node_modules:/mnt/d/Code/cbtest/counterdown/node_modules/.pnpm/@unocss+cli@66.3.3/node_modules/@unocss/node_modules:/mnt/d/Code/cbtest/counterdown/node_modules/.pnpm/@unocss+cli@66.3.3/node_modules:/mnt/d/Code/cbtest/counterdown/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/Code/cbtest/counterdown/node_modules/.pnpm/@unocss+cli@66.3.3/node_modules/@unocss/cli/bin/node_modules:/mnt/d/Code/cbtest/counterdown/node_modules/.pnpm/@unocss+cli@66.3.3/node_modules/@unocss/cli/node_modules:/mnt/d/Code/cbtest/counterdown/node_modules/.pnpm/@unocss+cli@66.3.3/node_modules/@unocss/node_modules:/mnt/d/Code/cbtest/counterdown/node_modules/.pnpm/@unocss+cli@66.3.3/node_modules:/mnt/d/Code/cbtest/counterdown/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/unocss.mjs" "$@"
else
  exec node  "$basedir/../../bin/unocss.mjs" "$@"
fi
